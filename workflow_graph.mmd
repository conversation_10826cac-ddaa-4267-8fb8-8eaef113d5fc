---
config:
  flowchart:
    curve: linear
---
graph TD;
	__start__([<p>__start__</p>]):::first
	file_parser(file_parser)
	account_bill(account_bill)
	work_statistics_agent(work_statistics_agent)
	work_statistics_tools(work_statistics_tools)
	payroll(payroll)
	calculation(calculation)
	__end__([<p>__end__</p>]):::last
	__start__ --> file_parser;
	account_bill --> payroll;
	file_parser -. &nbsp;error&nbsp; .-> __end__;
	file_parser -.-> account_bill;
	file_parser -. &nbsp;work_statistics&nbsp; .-> work_statistics_agent;
	payroll --> calculation;
	work_statistics_agent -.-> __end__;
	work_statistics_agent -. &nbsp;call_tool&nbsp; .-> work_statistics_tools;
	work_statistics_tools -.-> payroll;
	work_statistics_tools -.-> work_statistics_agent;
	calculation --> __end__;
	work_statistics_agent -.-> work_statistics_agent;
	classDef default fill:#f2f0ff,line-height:1.2
	classDef first fill-opacity:0
	classDef last fill:#bfb6fc
