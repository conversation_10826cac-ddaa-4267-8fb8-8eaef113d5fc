#!/usr/bin/env python3

import sys
import os
import subprocess

def main():
    """启动优化后的Streamlit Web界面"""
    try:
        # 检查环境变量
        if not os.getenv('MOONSHOT_API_KEY'):
            print("⚠️  警告: 未设置 MOONSHOT_API_KEY 环境变量")
            print("请在 .env 文件中设置您的API密钥")
        else:
            print("✅ MOONSHOT_API_KEY 已设置")
        
        print("\n🎨 启动优化后的Web界面...")
        print("📋 新界面特性:")
        print("  ✅ 侧边栏文件上传和控制")
        print("  ✅ 分类消息显示（处理步骤、系统日志、Agent交互）")
        print("  ✅ 实时进度指示器")
        print("  ✅ 清晰的界面分区")
        print("  ✅ 类似CLI的信息呈现")
        
        # 启动Streamlit应用
        cmd = [
            sys.executable, '-m', 'streamlit', 'run', 
            'streamlit_app.py',
            '--server.port=8501',
            '--server.address=localhost',
            '--browser.gatherUsageStats=false'
        ]
        
        print(f"\n🚀 应用将在 http://localhost:8501 启动")
        print("按 Ctrl+C 停止应用")
        print("-" * 50)
        
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n\n🛑 应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
