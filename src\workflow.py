from typing import Dict, Any
from langgraph.graph import StateGraph, END, START
from langgraph.checkpoint.memory import MemorySaver
from langchain_core.messages import HumanMessage, BaseMessage, ToolMessage
import json
import os

from .state import WorkflowState
from .database import DatabaseManager
from .agents.file_parser import FileParserAgent
from .agents.account_bill import AccountBillProcessor
# 导入重构后的 Agent 组件
from .agents.work_statistics import (
    work_statistics_agent_node,
    decide_next_step,
    tools as work_statistics_tools_list # 导入工具列表
)
from .agents.payroll import PayrollProcessor
from .agents.calculation import CalculationAgent
from .utils import logger


class HesuanWorkflow:
    """保理核额审查工作流"""
    
    def __init__(self):
        self.file_parser = FileParserAgent()
        self.account_bill_processor = AccountBillProcessor()
        self.payroll_processor = PayrollProcessor()
        self.calculation_agent = CalculationAgent()
        
        self.workflow = self._build_workflow()
        # 使用最简单的内存Checkpointer来支持中断和恢复
        self.checkpointer = MemorySaver()
        self.app = self.workflow.compile(
            checkpointer=self.checkpointer
        )
    
    def _build_workflow(self) -> StateGraph:
            """构建最终的、正确的、统一的工作流图（采用状态驱动路由）"""
            workflow = StateGraph(WorkflowState)
    
            # 1. 添加所有节点
            workflow.add_node("file_parser", self.file_parser)
            workflow.add_node("account_bill", self.account_bill_processor)
            workflow.add_node("work_statistics_agent", work_statistics_agent_node)
            # 使用我们自定义的、能够更新状态的工具节点
            workflow.add_node("work_statistics_tools", self.execute_work_statistics_tools)
            workflow.add_node("payroll", self.payroll_processor)
            workflow.add_node("calculation", self.calculation_agent)
    
            # 2. 定义边和路由
            workflow.add_edge(START, "file_parser")
    
            # 文件解析后的路由
            workflow.add_conditional_edges(
                "file_parser",
                self._route_after_parsing,
                {
                    "account_bill": "account_bill",
                    "work_statistics": "work_statistics_agent",
                    "error": END
                }
            )
    
            # Agent 思考后的决策路由 (已简化)
            workflow.add_conditional_edges(
                "work_statistics_agent",
                decide_next_step,
                {
                    "call_tool": "work_statistics_tools",
                    "work_statistics_agent": "work_statistics_agent",
                    END: END
                }
            )
    
            # 工具执行后的路由 (核心重构部分)
            workflow.add_conditional_edges(
                "work_statistics_tools",
                self.route_after_work_statistics_tools, # 使用新的、基于状态的路由函数
                {
                    "payroll": "payroll",
                    "work_statistics_agent": "work_statistics_agent"
                }
            )
    

    
            # 其余线性流程
            workflow.add_edge("account_bill", "payroll")
            workflow.add_edge("payroll", "calculation")
            workflow.add_edge("calculation", END)
    
            return workflow
    
    def _route_after_parsing(self, state: WorkflowState) -> str:
        """文件解析后的路由决策"""
        if state.get("errors"):
            logger.error(f"文件解析后发现错误，流程终止: {state['errors']}")
            return "error"
        
        classifications = state.get("sheet_classifications", {})
        logger.info(f"文件解析完成，分类结果: {classifications}")
        
        if "对账单" in classifications.values():
            return "account_bill"
        elif "用工统计表" in classifications.values():
            return "work_statistics"
        else:
            logger.error(f"无法根据分类确定下一步: {classifications}")
            return "error"


    def get_initial_state(self, file_path: str) -> WorkflowState:
        """创建工作流的初始状态"""
        return WorkflowState(
            file_path=file_path,
            project_id=0,
            project_name="",
            file_sheets={},
            sheet_classifications={},
            calculation_mode="",
            settlement_data=[],
            payroll_data=[],
            final_result={},
            errors=[],
            logs=[],
            current_step="started",
            messages=[],
            sheet_name=None,
            last_tool_name=None,
            last_tool_status=None
        )

    def run(self, file_path: str, stream_log: bool = False) -> Dict[str, Any]:
        """运行工作流"""
        try:
            initial_state = self.get_initial_state(file_path)
            return self.app.invoke(initial_state)
        except Exception as e:
            return {"errors": [f"工作流执行失败: {str(e)}"], "current_step": "error"}

    def stream_run(self, file_path: str):
        """流式执行工作流（用于实时观察进度）"""
        try:
            initial_state = self.get_initial_state(file_path)
            for output in self.app.stream(initial_state):
                yield output
        except Exception as e:
            yield {"error": {"errors": [f"工作流执行失败: {str(e)}"], "current_step": "error"}}

    def execute_work_statistics_tools(self, state: WorkflowState) -> dict:
        """自定义工具执行节点。它调用工具，解析元组，并更新状态。"""
        tool_invocation = state['messages'][-1].tool_calls[0]
        tool = {t.name: t for t in work_statistics_tools_list}[tool_invocation['name']]
        status, message = tool.invoke(tool_invocation['args'])
        tool_message = ToolMessage(content=message, tool_call_id=tool_invocation['id'])

        result = {
            "messages": [tool_message],
            "last_tool_name": tool.name,
            "last_tool_status": status
        }

        # 如果是执行工具且执行成功，需要更新状态
        execution_tools = [
            "process_with_multiplication",
            "process_with_fixed_rate",
            "process_from_direct_amount"
        ]

        if tool.name in execution_tools and status == "success":
            try:
                # 从数据库读取刚刚保存的结算数据
                db = DatabaseManager()
                project_id = state.get("project_id")
                settlement_data = db.get_settlement_amounts(project_id)

                if settlement_data:
                    # 计算总金额
                    settlement_total = sum(item['settlement_amount'] for item in settlement_data)

                    # 更新状态
                    result["settlement_data"] = settlement_data
                    result["current_step"] = "work_statistics_processed"

                    # 确保final_result存在并更新
                    final_result = state.get("final_result", {})
                    final_result["settlement_total"] = settlement_total
                    result["final_result"] = final_result

                    logger.info(f"已更新工作流状态：结算数据 {len(settlement_data)} 条，总金额 {settlement_total:,.2f} 元")

            except Exception as e:
                logger.warning(f"更新工作流状态时出错: {str(e)}")

        return result

    def route_after_work_statistics_tools(self, state: WorkflowState) -> str:
        """根据状态进行健壮的路由。"""
        tool_name = state.get("last_tool_name")
        tool_status = state.get("last_tool_status")
        logger.info(f"工具 '{tool_name}' 执行完毕，状态: {tool_status}。准备路由...")
        execution_tools = [
            "process_with_multiplication",
            "process_with_fixed_rate",
            "process_from_direct_amount"
        ]
        if tool_name in execution_tools and tool_status == "success":
            logger.success(f"执行工具 '{tool_name}' 成功，流转到 'payroll' 节点。")
            return "payroll"
        else:
            logger.info(f"返回 Agent 节点进行下一步处理。")
            return "work_statistics_agent"

    def draw_mermaid(self, output_path: str = "workflow_graph.mmd"):
        """
        生成工作流图的Mermaid代码并保存到文件

        Args:
            output_path: 输出文件路径
        """
        try:
            # 获取图的Mermaid表示
            mermaid_code = self.app.get_graph().draw_mermaid()

            # 保存Mermaid代码到文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(mermaid_code)

            logger.success(f"Mermaid图代码已保存到: {output_path}")
            logger.info("您可以将代码复制到 https://mermaid.live 进行可视化")

            return mermaid_code

        except Exception as e:
            logger.error(f"生成Mermaid代码失败: {e}")
            return None
