# 保理核额审查多Agent系统

基于LangGraph构建的智能保理核额审查系统，支持多种Excel表格格式的自动化处理。

## 功能特性

- **智能文件解析**: 自动识别Excel文件中的不同Sheet类型
- **多Agent协作**: 5个专业Agent协同处理不同数据类型
- **灵活适配**: 支持不同表格结构和字段命名
- **精确计算**: 严格按照保理业务规则进行核额计算
- **详细报告**: 生成完整的处理报告和结果输出
- **工作流可视化**: 支持生成Mermaid图表展示处理流程

## 核心特性

### 智能数据清洗与处理

本系统的核心优势在于其处理不规范Excel文件的鲁棒性，主要通过**智能数据清洗与处理**机制实现：

-   **智能清洗流程**:
    -   **LLM语义映射**: Agent以无表头模式加载Sheet的原始数据样本，发送给LLM，识别出关键业务字段（如"员工姓名"、"结算金额"）的**实际列名**。
    -   **代码精确定位**: Agent的Python代码接收到LLM返回的真实列名，逐行扫描原始数据，**精确定位**包含所有这些列名的表头行号。
    -   **可靠加载**: 使用定位到的行号作为`header`参数，重新加载整个Sheet，得到一个列名正确、可直接用于计算的DataFrame。
-   **数据计算与持久化**:
    -   **汇总行处理**: 根据LLM识别的汇总行标识（如"合计"），或通过空值判断，移除所有汇总行。
    -   **金额计算**: 逐行读取金额列，进行求和，得到**不受原始表格"合计"行影响**的精确结算总额。
    -   **数据库存储**: 将清洗和计算后的每条结算记录（员工、金额等）保存到SQLite数据库中，供后续Agent使用。

### 计算模式
- **账单模式-先发后融**: 对账单 + 发薪流水 (折算比例80%)
- **账单模式-先融后发**: 对账单 + 应发薪表 (折算比例80%)
- **非账单模式-先发后融**: 用工统计表 + 发薪流水 (折算比例70%)
- **非账单模式-先融后发**: 用工统计表 + 应发薪表 (折算比例70%)

## 安装和使用

### 环境要求
- Python 3.11+
- uv包管理器

### 安装依赖
```bash
uv sync
```

### 基本使用
```bash
# 处理Excel文件
python main.py process data.xlsx

# 详细输出模式
python main.py process data.xlsx --verbose

# 输出JSON格式
python main.py process data.xlsx --format json

# 生成详细报告
python main.py process data.xlsx --format report --save

# 演示模式
python main.py demo
```

### 工作流可视化
```bash
# 生成工作流图的Mermaid代码
python main.py visualize

# 指定输出文件
python main.py visualize --output my_workflow.mmd
```

生成的Mermaid代码可以：
- 复制到 [Mermaid Live Editor](https://mermaid.live) 进行在线可视化
- 在支持Mermaid的Markdown编辑器中直接渲染
- 使用Mermaid CLI工具转换为图片格式

### 输出格式
- **table**: 表格形式显示结果
- **json**: JSON格式输出
- **report**: 详细报告格式

## 项目结构

```
project-hesuan/
├── src/
│   ├── agents/           # Agent实现
│   │   ├── file_parser.py
│   │   ├── account_bill.py
│   │   ├── work_statistics.py
│   │   ├── payroll.py
│   │   └── calculation.py
│   ├── workflow.py       # 工作流编排
│   ├── state.py         # 状态定义
│   ├── database.py      # 数据库管理
│   └── cli.py           # 命令行界面
├── database/
│   └── schema.sql       # 数据库结构
├── testdata/           # 测试数据
├── tests/              # 测试用例
├── docs/               # 文档
│   └── 代码架构文档.md  # 详细架构说明
└── main.py             # 主入口
```

## 技术栈

- **LangGraph**: 多Agent工作流编排
- **LangChain**: LLM集成和调用
- **pandas**: Excel数据处理
- **SQLite**: 本地数据存储
- **Typer**: 命令行界面
- **Rich**: 美化终端输出

## 开发说明

### 运行测试
```bash
uv run pytest tests/
```

### 代码格式化
```bash
uv run black src/
uv run ruff src/
```

## 文档

- [代码架构文档](docs/代码架构文档.md) - 详细的系统架构和设计说明

## 许可证

本项目采用MIT许可证。
