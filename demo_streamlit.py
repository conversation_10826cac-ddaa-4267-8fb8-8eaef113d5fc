#!/usr/bin/env python3

import sys
import os
import subprocess
import time
import webbrowser
from pathlib import Path

def check_environment():
    """检查环境配置"""
    print("🔍 检查环境配置...")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version.major < 3 or python_version.minor < 10:
        print(f"❌ Python版本过低: {python_version.major}.{python_version.minor}")
        print("需要Python 3.10或更高版本")
        return False
    else:
        print(f"✅ Python版本: {python_version.major}.{python_version.minor}")
    
    # 检查API密钥
    if not os.getenv('MOONSHOT_API_KEY'):
        print("⚠️  警告: 未设置 MOONSHOT_API_KEY 环境变量")
        print("请在 .env 文件中设置您的API密钥")
        print("示例: MOONSHOT_API_KEY=your_api_key_here")
    else:
        print("✅ MOONSHOT_API_KEY 已设置")
    
    # 检查测试数据
    test_files = list(Path("testdata").glob("*.xlsx"))
    if test_files:
        print(f"✅ 找到 {len(test_files)} 个测试文件")
        for file in test_files[:3]:  # 显示前3个
            print(f"   - {file.name}")
        if len(test_files) > 3:
            print(f"   - ... 还有 {len(test_files) - 3} 个文件")
    else:
        print("⚠️  警告: 未找到测试数据文件")
    
    return True

def install_dependencies():
    """安装依赖"""
    print("\n📦 检查依赖...")
    
    try:
        import streamlit
        print("✅ Streamlit 已安装")
        return True
    except ImportError:
        print("❌ Streamlit 未安装，正在安装...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "streamlit"], 
                         check=True, capture_output=True)
            print("✅ Streamlit 安装成功")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 安装失败: {e}")
            return False

def start_demo():
    """启动演示"""
    print("\n🚀 启动Streamlit演示...")
    
    # 应用路径
    app_path = Path("src/streamlit_app.py")
    if not app_path.exists():
        print(f"❌ 应用文件不存在: {app_path}")
        return False
    
    # 启动命令
    cmd = [
        sys.executable, "-m", "streamlit", "run", 
        str(app_path),
        "--server.port=8501",
        "--server.address=localhost",
        "--browser.gatherUsageStats=false"
    ]
    
    print("启动命令:", " ".join(cmd))
    print("应用将在 http://localhost:8501 启动")
    print("\n📝 使用说明:")
    print("1. 等待浏览器自动打开，或手动访问 http://localhost:8501")
    print("2. 上传testdata目录中的任意Excel文件")
    print("3. 点击'开始处理'按钮")
    print("4. 观察Agent的处理过程")
    print("5. 如果Agent询问问题，请在输入框中回答")
    print("6. 查看最终的处理结果")
    print("\n按 Ctrl+C 停止演示")
    print("-" * 50)
    
    try:
        # 启动应用
        process = subprocess.Popen(cmd)
        
        # 等待应用启动
        print("等待应用启动...")
        time.sleep(3)
        
        # 尝试打开浏览器
        try:
            webbrowser.open("http://localhost:8501")
            print("✅ 浏览器已打开")
        except:
            print("⚠️  无法自动打开浏览器，请手动访问 http://localhost:8501")
        
        # 等待用户停止
        process.wait()
        
    except KeyboardInterrupt:
        print("\n\n🛑 演示已停止")
        try:
            process.terminate()
        except:
            pass
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
    
    return True

def show_help():
    """显示帮助信息"""
    print("""
🎯 保理核额审查系统 - Streamlit Web界面演示

这个演示将启动一个Web界面，让您可以通过浏览器使用保理核额审查系统。

📋 演示流程:
1. 检查环境配置
2. 安装必要依赖
3. 启动Streamlit应用
4. 在浏览器中体验功能

🔧 环境要求:
- Python 3.10+
- MOONSHOT_API_KEY 环境变量（可选，用于完整功能）
- 网络连接（用于安装依赖）

📁 测试数据:
演示会使用 testdata/ 目录中的Excel文件作为测试数据。

🌐 Web界面特性:
- 拖拽上传Excel文件
- 实时显示处理过程
- 智能Agent交互
- 多格式结果展示
- 用户友好的界面设计

❓ 如需帮助，请查看 docs/streamlit使用说明.md
""")

def main():
    """主函数"""
    print("🎉 欢迎使用保理核额审查系统 - Streamlit Web界面演示")
    print("=" * 60)
    
    # 显示帮助
    show_help()
    
    # 询问是否继续
    try:
        response = input("\n是否开始演示？(y/n): ").strip().lower()
        if response not in ['y', 'yes', '是', '']:
            print("演示已取消")
            return
    except KeyboardInterrupt:
        print("\n演示已取消")
        return
    
    # 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败，请解决上述问题后重试")
        return
    
    # 安装依赖
    if not install_dependencies():
        print("\n❌ 依赖安装失败")
        return
    
    # 启动演示
    if start_demo():
        print("\n🎉 演示完成！")
        print("\n📚 更多信息:")
        print("- 使用说明: docs/streamlit使用说明.md")
        print("- CLI版本: python main.py process testdata/测试V2.xlsx")
        print("- 项目文档: docs/代码架构文档.md")
    else:
        print("\n❌ 演示启动失败")

if __name__ == "__main__":
    main()
