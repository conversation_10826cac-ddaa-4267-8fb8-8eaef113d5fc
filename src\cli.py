#!/usr/bin/env python3

import typer
import json
import os
from pathlib import Path
from dotenv import load_dotenv
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.prompt import Prompt
from rich.syntax import Syntax

from .workflow import HesuanWorkflow
from langchain_core.messages import AIMessage
from langgraph.types import Command

# 加载 .env 文件中的环境变量
load_dotenv()

app = typer.Typer(help="保理核额审查多Agent系统")
console = Console()

@app.command()
def process(
    file_path: str = typer.Argument(..., help="Excel文件路径"),
    output_format: str = typer.Option("table", "--format", "-f", help="输出格式: table, json, report"),
    verbose: bool = typer.Option(False, "--verbose", "-v", help="详细输出"),
    save_report: bool = typer.Option(False, "--save", "-s", help="保存详细报告到文件")
):
    """
    处理保理核额审查Excel文件
    
    Examples:
        hesuan process data.xlsx
        hesuan process data.xlsx --format json
        hesuan process data.xlsx --verbose --save
    """
    # 验证文件路径
    if not os.path.exists(file_path):
        console.print(f"[red]错误: 文件不存在 {file_path}[/red]")
        raise typer.Exit(1)
    
    if not file_path.endswith(('.xlsx', '.xls')):
        console.print(f"[red]错误: 不支持的文件格式，请使用 .xlsx 或 .xls 文件[/red]")
        raise typer.Exit(1)
    
    # 显示处理开始信息
    console.print(f"[green]开始处理文件: {file_path}[/green]")
    console.print()
    
    # 创建工作流实例
    workflow = HesuanWorkflow()

    if verbose:
        console.print("[blue]详细模式：显示处理步骤[/blue]")
    else:
        console.print("[blue]开始执行工作流...[/blue]")
    console.print()

    step_names = {
        "file_parser": "📁 文件解析与分类",
        "account_bill": "📊 对账单处理",
        "work_statistics": "📈 用工统计表处理",
        "payroll": "💰 发薪流水处理",
        "calculation": "🧮 核算与输出"
    }

    # 为本次处理创建一个基于文件路径的确定性会话ID
    thread_id = f"thread_{os.path.abspath(file_path)}".encode('utf-8').hex()
    config = {"configurable": {"thread_id": thread_id}}

    # 初始化状态并启动工作流
    inputs = workflow.get_initial_state(file_path)
    stream = workflow.app.stream(inputs, config=config, stream_mode="values")
    final_result = None

    while True:
        # 消费当前流的所有事件
        for event in stream:
            if verbose:
                console.print("\n[cyan]-- 工作流步骤输出 (event):[/cyan]")
                console.print(event)
            final_result = event

        # 检查最后的状态，准备下一次迭代或结束
        messages = final_result.get("messages", [])
        if not messages:
            console.print("[bold green]*** 工作流已完成 (无消息) ***[/bold green]")
            break

        last_message = messages[-1]

        # 核心修复：采用更健壮的、分步的检查逻辑
        # 1. 首先，必须是 AIMessage
        if not isinstance(last_message, AIMessage):
            # 如果最后一条不是 AI 的回复（比如是 ToolMessage 或 HumanMessage），说明流程已结束
            console.print("[bold green]*** 工作流已完成 ***[/bold green]")
            break

        # 2. 其次，必须有工具调用
        if not last_message.tool_calls:
            # 如果是 AIMessage 但没有工具调用，说明是最终答案
            console.print("[bold green]*** 工作流已完成 (最终答案) ***[/bold green]")
            break

        # 3. 最后，检查是不是我们需要处理的特定工具
        tool_call = last_message.tool_calls[0]
        if tool_call['name'] == 'ask_user_for_clarification':
            console.print("[bold yellow]*** 模型请求人工干预 ***[/bold yellow]")
            question = tool_call['args']['question']
            console.print(Panel(f"[bold yellow]需要您的帮助[/bold yellow]\n\n{question}", title="[cyan]Agent请求[/cyan]", border_style="cyan"))
            
            user_response = Prompt.ask("[bold]您的回答[/bold]")

            console.print("[blue]感谢您的输入，正在恢复工作流...[/blue]")
            # 使用Command(resume=...)的标准方式恢复执行
            stream = workflow.app.stream(Command(resume=user_response), config=config, stream_mode="values")
            continue
        else:
            # 如果是其他类型的工具调用，目前的设计是直接结束
            console.print(f"[yellow]未处理的工具调用: {tool_call['name']}，流程结束。[/yellow]")
            break

    # 检查是否有错误
    if isinstance(final_result, dict) and final_result.get("errors"):
        console.print(f"[red]处理失败:[/red]")
        for error in final_result["errors"]:
            console.print(f"[red]  - {error}[/red]")
        raise typer.Exit(1)
    elif not isinstance(final_result, dict):
        console.print(f"[yellow]工作流未返回有效的最终结果。[/yellow]")
        raise typer.Exit(0)
    
    # 输出结果
    result_data = final_result.get("final_result", {})
    
    if output_format == "json":
        display_json_result(result_data)
    elif output_format == "report":
        display_report_result(result_data)
    
    # 保存详细报告
    if save_report:
        save_detailed_report(file_path, result_data)

def display_table_result(result_data: dict):
    """以表格形式显示结果"""
    formatted_result = result_data.get("formatted_result", {})
    
    table = Table(title="保理核额审查结果", show_header=True)
    table.add_column("项目", style="cyan")
    table.add_column("数值", style="magenta")
    table.add_column("说明", style="green")
    
    # 添加行数据
    table.add_row("项目名称", formatted_result.get("项目名称", ""), "")
    table.add_row("核额模式", formatted_result.get("核额模式", ""), "")
    table.add_row("结算总额", f"{formatted_result.get('结算总额', 0):,.2f} 元", "")
    table.add_row("折算比例", f"{formatted_result.get('折算比例', 0)*100:.0f}%", "")
    table.add_row("折后金额", f"{formatted_result.get('折后金额', 0):,.2f} 元", "")
    table.add_row("发薪金额", f"{formatted_result.get('发薪金额', 0):,.2f} 元", "")
    table.add_row("最终核额金额", f"{formatted_result.get('最终核额金额', 0):,.2f} 元", "核定结果")
    
    conflict_text = "是" if formatted_result.get("汇总金额是否冲突", False) else "否"
    table.add_row("汇总金额冲突", conflict_text, "")
    table.add_row("匹配员工数", str(formatted_result.get("匹配员工数", 0)), "")
    
    console.print(table)

def display_json_result(result_data: dict):
    """以JSON形式显示结果"""
    formatted_result = result_data.get("formatted_result", {})
    
    json_output = json.dumps(formatted_result, ensure_ascii=False, indent=2)
    syntax = Syntax(json_output, "json", theme="monokai", line_numbers=True)
    
    console.print(Panel(syntax, title="JSON输出", border_style="blue"))

def display_report_result(result_data: dict):
    """显示详细报告"""
    detailed_report = result_data.get("detailed_report", "")
    
    if detailed_report:
        console.print(Panel(detailed_report, title="详细报告", border_style="green"))
    else:
        console.print("[yellow]未生成详细报告[/yellow]")

def save_detailed_report(file_path: str, result_data: dict):
    """保存详细报告到文件"""
    detailed_report = result_data.get("detailed_report", "")
    
    if detailed_report:
        # 生成报告文件名
        base_name = os.path.splitext(os.path.basename(file_path))[0]
        report_path = f"{base_name}_核额报告.txt"
        
        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(detailed_report)
            
            console.print(f"[green]详细报告已保存到: {report_path}[/green]")
        except Exception as e:
            console.print(f"[red]保存报告失败: {str(e)}[/red]")
    else:
        console.print("[yellow]没有详细报告可保存[/yellow]")

@app.command()
def demo():
    """运行演示模式"""
    console.print("[blue]保理核额审查系统演示[/blue]")
    console.print()
    
    # 检查是否有测试数据
    test_file = "testdata/测试V2.xlsx"
    if os.path.exists(test_file):
        console.print(f"[green]找到测试文件: {test_file}[/green]")
        
        if Prompt.ask("是否使用测试文件进行演示？", choices=["y", "n"], default="y") == "y":
            console.print()
            console.print("[yellow]开始演示...[/yellow]")
            
            # 调用处理命令
            process(test_file, output_format="table", verbose=True, save_report=True)
        else:
            console.print("演示已取消")
    else:
        console.print(f"[red]测试文件不存在: {test_file}[/red]")
        console.print("请将测试文件放在 testdata 目录下")

@app.command()
def visualize(
    output_path: str = typer.Option("workflow_graph.mmd", "--output", "-o", help="输出文件路径")
):
    """
    生成工作流图的Mermaid代码

    Examples:
        hesuan visualize                           # 生成 workflow_graph.mmd
        hesuan visualize --output my_graph.mmd     # 指定输出文件
    """
    console.print("[blue]正在生成工作流图Mermaid代码...[/blue]")

    try:
        # 设置临时API密钥避免初始化错误
        os.environ.setdefault("MOONSHOT_API_KEY", "dummy_key_for_visualization")

        # 创建工作流实例
        workflow = HesuanWorkflow()

        # 生成Mermaid代码
        mermaid_code = workflow.draw_mermaid(output_path)

        if mermaid_code:
            console.print(f"[green]✅ Mermaid代码生成成功![/green]")
            console.print("[yellow]您可以将代码复制到 https://mermaid.live 进行在线可视化[/yellow]")

            # 显示Mermaid代码预览
            console.print("\n[blue]Mermaid代码预览:[/blue]")
            syntax = Syntax(mermaid_code, "mermaid", theme="monokai", line_numbers=True)
            console.print(Panel(syntax, title="Mermaid Graph Code", border_style="blue"))
        else:
            console.print(f"[red]生成失败[/red]")
            raise typer.Exit(1)

    except Exception as e:
        console.print(f"[red]可视化失败: {str(e)}[/red]")
        raise typer.Exit(1)

@app.command()
def version():
    """显示版本信息"""
    console.print("[blue]保理核额审查多Agent系统 v0.1.0[/blue]")
    console.print("基于 LangGraph 构建的智能工作流系统")

if __name__ == "__main__":
    app()