import sqlite3
import os
from typing import List, Dict, Any, Optional
from datetime import datetime

class DatabaseManager:
    """数据库管理器 - 使用短连接模式避免线程问题"""

    def __init__(self, db_path: str = "database/hesuan.db"):
        self.db_path = db_path
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        self._init_database()

    def _get_connection(self):
        """获取数据库连接"""
        return sqlite3.connect(self.db_path)

    def _init_database(self):
        """初始化数据库"""
        # 获取当前文件的目录，然后构建schema.sql的路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(current_dir)
        schema_path = os.path.join(project_root, "database", "schema.sql")

        with open(schema_path, 'r', encoding='utf-8') as f:
            schema_sql = f.read()

        with self._get_connection() as conn:
            conn.executescript(schema_sql)

    def close(self):
        """关闭数据库连接 - 短连接模式下不需要"""
        pass  # 保持接口兼容性，但不执行任何操作

    def create_project(self, project_name: str, file_path: str) -> int:
        """创建项目记录"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "INSERT INTO projects (project_name, file_path) VALUES (?, ?)",
                (project_name, file_path)
            )
            conn.commit()
            return cursor.lastrowid

    def save_employees(self, project_id: int, employees: List[Dict[str, Any]]):
        """保存员工信息"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM employees WHERE project_id = ?", (project_id,))

            for emp in employees:
                cursor.execute(
                    "INSERT INTO employees (project_id, employee_id, employee_name) VALUES (?, ?, ?)",
                    (project_id, emp.get('employee_id'), emp.get('employee_name'))
                )
            conn.commit()

    def save_settlement_amounts(self, project_id: int, settlement_data: List[Dict[str, Any]]):
        """保存结算金额数据 (主要用于 account_bill)"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            # 为了幂等性，可以先删除旧数据，但要小心 source_type
            cursor.execute("DELETE FROM settlement_amounts WHERE project_id = ? AND source_type = ?",
                           (project_id, 'account_bill'))

            for data in settlement_data:
                cursor.execute(
                    """INSERT INTO settlement_amounts
                       (project_id, employee_id, employee_name, settlement_amount, source_type)
                       VALUES (?, ?, ?, ?, ?)""",
                    (project_id, data.get('employee_id'), data.get('employee_name'),
                     data.get('settlement_amount'), data.get('source_type'))
                )
            conn.commit()

    def save_preprocessed_work_stats(self, project_id: int, preprocessed_data: List[Dict[str, Any]]):
        """保存用工统计表的预处理数据，用于后续计算"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM settlement_amounts WHERE project_id = ? AND source_type = ?",
                           (project_id, 'work_statistics'))

            for data in preprocessed_data:
                cursor.execute(
                    """INSERT INTO settlement_amounts
                       (project_id, employee_id, employee_name, source_type,
                        preprocessed_quantity, preprocessed_price, preprocessed_amount)
                       VALUES (?, ?, ?, ?, ?, ?, ?)""",
                    (project_id, data.get('employee_id'), data.get('employee_name'), 'work_statistics',
                     data.get('quantity'), data.get('price'), data.get('amount'))
                )
            conn.commit()

    def update_calculated_settlement_amounts(self, project_id: int, calculated_data: List[Dict[str, Any]]):
        """将计算出的最终结算金额更新回数据库"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            for data in calculated_data:
                cursor.execute(
                    """UPDATE settlement_amounts
                       SET settlement_amount = ?
                       WHERE project_id = ? AND employee_name = ? AND source_type = 'work_statistics'""",
                    (data['settlement_amount'], project_id, data['employee_name'])
                )
            conn.commit()

    def save_payroll_amounts(self, project_id: int, payroll_data: List[Dict[str, Any]]):
        """保存发薪金额数据"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM payroll_amounts WHERE project_id = ?", (project_id,))

            for data in payroll_data:
                cursor.execute(
                    """INSERT INTO payroll_amounts
                       (project_id, employee_id, employee_name, payroll_amount, source_type)
                       VALUES (?, ?, ?, ?, ?)""",
                    (project_id, data.get('employee_id'), data.get('employee_name'),
                     data.get('payroll_amount'), data.get('source_type'))
                )
            conn.commit()

    def save_calculation_result(self, project_id: int, result: Dict[str, Any]):
        """保存计算结果"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM calculation_results WHERE project_id = ?", (project_id,))

            cursor.execute(
                """INSERT INTO calculation_results
                   (project_id, project_name, calculation_mode, settlement_total,
                    discount_rate, discounted_amount, payroll_total, final_credit_amount,
                    summary_conflict, matched_employees)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                (project_id, result.get('project_name'), result.get('calculation_mode'),
                 result.get('settlement_total'), result.get('discount_rate'),
                 result.get('discounted_amount'), result.get('payroll_total'),
                 result.get('final_credit_amount'), result.get('summary_conflict'),
                 str(result.get('matched_employees')))
            )
            conn.commit()

    def get_employees(self, project_id: int) -> List[Dict[str, Any]]:
        """获取员工信息"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT employee_id, employee_name FROM employees WHERE project_id = ?",
                (project_id,)
            )
            return [{'employee_id': row[0], 'employee_name': row[1]} for row in cursor.fetchall()]

    def get_settlement_amounts(self, project_id: int) -> List[Dict[str, Any]]:
        """获取所有最终结算金额数据"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                """SELECT employee_id, employee_name, settlement_amount, source_type
                   FROM settlement_amounts WHERE project_id = ? AND settlement_amount IS NOT NULL""",
                (project_id,)
            )
            return [{'employee_id': row[0], 'employee_name': row[1],
                    'settlement_amount': row[2], 'source_type': row[3]}
                    for row in cursor.fetchall()]

    def get_preprocessed_work_stats(self, project_id: int) -> List[Dict[str, Any]]:
        """获取用工统计表的预处理数据用于计算"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                """SELECT id, employee_id, employee_name, preprocessed_quantity, preprocessed_price, preprocessed_amount
                   FROM settlement_amounts
                   WHERE project_id = ? AND source_type = 'work_statistics' AND settlement_amount IS NULL""",
                (project_id,)
            )
            return [{'db_id': row[0], 'employee_id': row[1], 'employee_name': row[2],
                     'quantity': row[3], 'price': row[4], 'amount': row[5]}
                    for row in cursor.fetchall()]

    def get_payroll_amounts(self, project_id: int) -> List[Dict[str, Any]]:
        """获取发薪金额数据"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                """SELECT employee_id, employee_name, payroll_amount, source_type
                   FROM payroll_amounts WHERE project_id = ?""",
                (project_id,)
            )
            return [{'employee_id': row[0], 'employee_name': row[1],
                    'payroll_amount': row[2], 'source_type': row[3]}
                    for row in cursor.fetchall()]