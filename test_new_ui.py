#!/usr/bin/env python3

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_streamlit_import():
    """测试新界面的导入"""
    try:
        print("🧪 测试新Streamlit界面...")
        
        # 测试基本导入
        import streamlit as st
        print("✅ Streamlit导入成功")
        
        # 测试新界面函数导入
        from streamlit_app import (
            initialize_session_state,
            add_process_step,
            add_system_log,
            add_agent_message,
            show_sidebar,
            show_main_interface
        )
        print("✅ 新界面函数导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def main():
    """主测试函数"""
    print("🎨 测试新的Streamlit界面设计...")
    
    if test_streamlit_import():
        print("\n🎉 新界面测试通过！")
        print("\n📋 新界面特性:")
        print("✅ 侧边栏文件上传")
        print("✅ 分类消息显示")
        print("✅ 处理步骤可视化")
        print("✅ 清晰的界面分区")
        print("✅ 实时进度更新")
        
        print("\n🚀 启动新界面:")
        print("streamlit run streamlit_app.py")
        
        return True
    else:
        print("\n❌ 新界面测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
