#!/usr/bin/env python3

import sys
import os
import tempfile
import threading
import time

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.database import DatabaseManager

def test_database_in_thread(thread_id, results):
    """在不同线程中测试数据库操作"""
    try:
        print(f"线程 {thread_id}: 开始测试")
        
        # 使用临时数据库文件
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp:
            db_path = tmp.name
        
        # 创建数据库管理器
        db = DatabaseManager(db_path)
        
        # 测试创建项目
        project_id = db.create_project(f"测试项目_{thread_id}", f"test_{thread_id}.xlsx")
        print(f"线程 {thread_id}: 创建项目成功，ID={project_id}")
        
        # 测试保存员工信息
        employees = [
            {"employee_id": f"00{thread_id}1", "employee_name": f"张三_{thread_id}"},
            {"employee_id": f"00{thread_id}2", "employee_name": f"李四_{thread_id}"}
        ]
        db.save_employees(project_id, employees)
        print(f"线程 {thread_id}: 保存员工信息成功")
        
        # 测试获取员工信息
        retrieved_employees = db.get_employees(project_id)
        print(f"线程 {thread_id}: 获取员工信息成功，数量={len(retrieved_employees)}")
        
        # 测试保存结算数据
        settlement_data = [
            {
                "employee_id": f"00{thread_id}1",
                "employee_name": f"张三_{thread_id}",
                "settlement_amount": 1000.0 + thread_id,
                "source_type": "account_bill"
            }
        ]
        db.save_settlement_amounts(project_id, settlement_data)
        print(f"线程 {thread_id}: 保存结算数据成功")
        
        # 测试获取结算数据
        retrieved_settlement = db.get_settlement_amounts(project_id)
        print(f"线程 {thread_id}: 获取结算数据成功，数量={len(retrieved_settlement)}")
        
        # 清理临时文件
        os.unlink(db_path)
        
        results[thread_id] = "成功"
        print(f"线程 {thread_id}: 所有测试完成")
        
    except Exception as e:
        results[thread_id] = f"失败: {str(e)}"
        print(f"线程 {thread_id}: 测试失败 - {str(e)}")

def main():
    """主测试函数"""
    print("🧪 开始测试数据库线程安全修复...")
    
    # 创建多个线程同时测试数据库操作
    threads = []
    results = {}
    
    # 启动5个线程
    for i in range(5):
        thread = threading.Thread(target=test_database_in_thread, args=(i, results))
        threads.append(thread)
        thread.start()
        time.sleep(0.1)  # 稍微错开启动时间
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    # 输出结果
    print("\n📊 测试结果:")
    success_count = 0
    for thread_id, result in results.items():
        if result == "成功":
            print(f"✅ 线程 {thread_id}: {result}")
            success_count += 1
        else:
            print(f"❌ 线程 {thread_id}: {result}")
    
    print(f"\n总结: {success_count}/{len(results)} 个线程测试成功")
    
    if success_count == len(results):
        print("🎉 所有线程测试通过！数据库线程安全问题已修复。")
        return True
    else:
        print("❌ 部分线程测试失败，可能仍存在线程安全问题。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
