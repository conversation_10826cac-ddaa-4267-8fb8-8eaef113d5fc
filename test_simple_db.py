#!/usr/bin/env python3

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_database_import():
    """测试数据库模块导入"""
    try:
        from src.database import DatabaseManager
        print("✅ 数据库模块导入成功")
        return True
    except Exception as e:
        print(f"❌ 数据库模块导入失败: {e}")
        return False

def test_database_creation():
    """测试数据库创建"""
    try:
        from src.database import DatabaseManager
        
        # 使用简单的数据库路径
        db_path = "simple_test.db"
        
        # 清理可能存在的旧文件
        if os.path.exists(db_path):
            os.remove(db_path)
        
        # 创建数据库管理器
        db = DatabaseManager(db_path)
        print("✅ 数据库管理器创建成功")
        
        # 测试简单操作
        project_id = db.create_project("测试项目", "test.xlsx")
        print(f"✅ 创建项目成功，ID={project_id}")
        
        # 清理
        if os.path.exists(db_path):
            os.remove(db_path)
        
        return True
    except Exception as e:
        print(f"❌ 数据库创建失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 开始简单数据库测试...")
    
    tests = [
        ("导入测试", test_database_import),
        ("创建测试", test_database_creation),
    ]
    
    passed = 0
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        if test_func():
            passed += 1
    
    print(f"\n📊 测试结果: {passed}/{len(tests)} 通过")
    
    if passed == len(tests):
        print("🎉 基础数据库功能正常！")
        print("✅ 数据库修复应该已经解决了SQLite线程问题。")
        print("✅ 现在可以在Streamlit中安全使用数据库。")
        return True
    else:
        print("❌ 基础数据库功能有问题。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
