#!/usr/bin/env python3

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def debug_paths():
    """调试路径问题"""
    print("🔍 调试数据库路径问题...")
    
    # 当前工作目录
    cwd = os.getcwd()
    print(f"当前工作目录: {cwd}")
    
    # 脚本目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    print(f"脚本目录: {script_dir}")
    
    # src目录
    src_dir = os.path.join(script_dir, 'src')
    print(f"src目录: {src_dir}")
    print(f"src目录存在: {os.path.exists(src_dir)}")
    
    # database目录
    db_dir = os.path.join(script_dir, 'database')
    print(f"database目录: {db_dir}")
    print(f"database目录存在: {os.path.exists(db_dir)}")
    
    # schema.sql文件
    schema_path = os.path.join(db_dir, 'schema.sql')
    print(f"schema.sql路径: {schema_path}")
    print(f"schema.sql存在: {os.path.exists(schema_path)}")
    
    # 测试从src/database.py的角度
    database_py = os.path.join(src_dir, 'database.py')
    print(f"database.py路径: {database_py}")
    print(f"database.py存在: {os.path.exists(database_py)}")
    
    # 模拟database.py中的路径计算
    current_dir = os.path.dirname(os.path.abspath(database_py))
    project_root = os.path.dirname(current_dir)
    calculated_schema_path = os.path.join(project_root, "database", "schema.sql")
    print(f"计算的schema路径: {calculated_schema_path}")
    print(f"计算的schema存在: {os.path.exists(calculated_schema_path)}")

def test_database_with_debug():
    """测试数据库创建并输出调试信息"""
    try:
        # 先调试路径
        debug_paths()
        
        print("\n🧪 测试数据库创建...")
        from src.database import DatabaseManager
        
        # 使用简单的数据库路径
        db_path = "debug_test.db"
        
        # 清理可能存在的旧文件
        if os.path.exists(db_path):
            os.remove(db_path)
        
        # 创建数据库管理器
        db = DatabaseManager(db_path)
        print("✅ 数据库管理器创建成功")
        
        # 清理
        if os.path.exists(db_path):
            os.remove(db_path)
        
        return True
    except Exception as e:
        print(f"❌ 数据库创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_database_with_debug()
