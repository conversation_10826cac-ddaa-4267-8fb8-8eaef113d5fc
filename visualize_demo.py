#!/usr/bin/env python3
"""
工作流图可视化演示脚本
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# 设置一个临时的API密钥环境变量，避免初始化错误
os.environ.setdefault("MOONSHOT_API_KEY", "dummy_key_for_visualization")

from src.workflow import HesuanWorkflow
from src.utils import logger

def main():
    """演示工作流图可视化功能"""

    logger.info("=== 保理核额审查工作流图可视化演示 ===\n")

    try:
        # 创建工作流实例
        logger.info("正在创建工作流实例...")
        workflow = HesuanWorkflow()
        logger.success("工作流实例创建成功!")

        # 生成Mermaid代码
        logger.info("\n生成Mermaid图代码:")
        mermaid_code = workflow.draw_mermaid("workflow_graph.mmd")

        if mermaid_code:
            # 显示代码预览
            logger.info("\nMermaid代码预览:")
            print("=" * 50)
            print(mermaid_code)
            print("=" * 50)

            # 使用说明
            logger.info("\n=== 使用说明 ===")
            logger.info("1. 查看生成的 workflow_graph.mmd 文件")
            logger.info("2. 将Mermaid代码复制到 https://mermaid.live 进行在线可视化")
            logger.info("3. 或者使用CLI命令: python main.py visualize")
        else:
            logger.error("生成Mermaid代码失败")
            return 1

    except Exception as e:
        logger.error(f"演示过程中出错: {e}")
        return 1

    return 0

if __name__ == "__main__":
    exit(main())
