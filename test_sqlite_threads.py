#!/usr/bin/env python3

import sys
import os
import threading
import time

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.database import DatabaseManager

def test_database_operations(thread_id, shared_db_path, results):
    """在不同线程中测试数据库操作"""
    try:
        print(f"线程 {thread_id}: 开始测试")
        
        # 使用共享的数据库文件
        db = DatabaseManager(shared_db_path)
        
        # 测试创建项目
        project_id = db.create_project(f"测试项目_{thread_id}_{int(time.time())}", f"test_{thread_id}.xlsx")
        print(f"线程 {thread_id}: 创建项目成功，ID={project_id}")
        
        # 测试保存员工信息
        employees = [
            {"employee_id": f"T{thread_id}_001", "employee_name": f"员工A_{thread_id}"},
            {"employee_id": f"T{thread_id}_002", "employee_name": f"员工B_{thread_id}"}
        ]
        db.save_employees(project_id, employees)
        print(f"线程 {thread_id}: 保存员工信息成功")
        
        # 测试获取员工信息
        retrieved_employees = db.get_employees(project_id)
        print(f"线程 {thread_id}: 获取员工信息成功，数量={len(retrieved_employees)}")
        
        # 测试保存结算数据
        settlement_data = [
            {
                "employee_id": f"T{thread_id}_001",
                "employee_name": f"员工A_{thread_id}",
                "settlement_amount": 1000.0 + thread_id * 100,
                "source_type": "account_bill"
            }
        ]
        db.save_settlement_amounts(project_id, settlement_data)
        print(f"线程 {thread_id}: 保存结算数据成功")
        
        # 测试获取结算数据
        retrieved_settlement = db.get_settlement_amounts(project_id)
        print(f"线程 {thread_id}: 获取结算数据成功，数量={len(retrieved_settlement)}")
        
        # 模拟一些延迟
        time.sleep(0.1)
        
        results[thread_id] = "成功"
        print(f"线程 {thread_id}: 所有测试完成")
        
    except Exception as e:
        results[thread_id] = f"失败: {str(e)}"
        print(f"线程 {thread_id}: 测试失败 - {str(e)}")

def main():
    """主测试函数"""
    print("🧪 开始测试SQLite线程安全修复...")
    
    # 使用固定的数据库文件路径
    shared_db_path = "test_threads.db"
    
    # 清理可能存在的旧文件
    if os.path.exists(shared_db_path):
        os.remove(shared_db_path)
    
    # 创建多个线程同时测试数据库操作
    threads = []
    results = {}
    
    print(f"使用共享数据库文件: {shared_db_path}")
    
    # 启动3个线程（减少数量避免过多并发）
    for i in range(3):
        thread = threading.Thread(target=test_database_operations, args=(i, shared_db_path, results))
        threads.append(thread)
        thread.start()
        time.sleep(0.05)  # 稍微错开启动时间
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    # 输出结果
    print("\n📊 测试结果:")
    success_count = 0
    for thread_id, result in results.items():
        if result == "成功":
            print(f"✅ 线程 {thread_id}: {result}")
            success_count += 1
        else:
            print(f"❌ 线程 {thread_id}: {result}")
    
    print(f"\n总结: {success_count}/{len(results)} 个线程测试成功")
    
    # 清理测试文件
    try:
        if os.path.exists(shared_db_path):
            os.remove(shared_db_path)
            print(f"✅ 清理测试文件: {shared_db_path}")
    except:
        print(f"⚠️  无法清理测试文件: {shared_db_path}")
    
    if success_count == len(results):
        print("🎉 所有线程测试通过！SQLite线程安全问题已修复。")
        print("✅ 数据库现在使用短连接模式，每次操作创建新连接。")
        print("✅ 这解决了'SQLite objects created in a thread can only be used in that same thread'错误。")
        return True
    else:
        print("❌ 部分线程测试失败，可能仍存在问题。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
