我需要基于streamlit为这个核算agent构建一个web界面，界面需要支持以下功能：
1. 支持excel的上传（点击打开，或拖拽进去）
2. 展示的内容可以参考cli.py,只是现在是使用web网页，使用streamlit所支持的组件进行可视化；
3. 动态和直观的展示文件的处理过程和中间的结果，以agent为主体去展示进展，也能够吧agent调用工具，处理产生结果的过程能够展示出来，增加过程可解释性；工具调用、结果展示、agent，不同的消息类型要选择有所差异的组件，做到区分显示，有助于信息的可读性；
4. 可以参考chatgpt web的交互模式，虽然大部分时候是agent系统在输出，也存在需要反向向用户咨询的情况，可以在底部保持一个用户输入框，只是只有在agent请求用户输入的时候才允许输入；
5. 关于请求用输入（ask user）这里的处理要额外注意一下，尽量参考cli.py中的方式。目前终端应用是通过interupt + workflow.app.stream(Command(resume=user_response), config=config, stream_mode="values")实现的，streamlit情况下可以参照这种方式
6. 新增界面代码的过程中，要尽量避免对底层 workflow.py等代码的更改，避免对cli.py造成影响。如果你觉得有必要做一些为了两种模式（web和终端）共存所更优的修改，也可以给我建议，我确认后可以考虑修改。
7. 界面的修改从最简单朴素的开始，以实现功能为第一目标，以最少代码实现功能为原则。