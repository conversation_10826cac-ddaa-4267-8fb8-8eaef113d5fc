#!/usr/bin/env python3

import sys
import os
import subprocess

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    """启动Streamlit应用"""
    try:
        # 检查环境变量
        if not os.getenv('MOONSHOT_API_KEY'):
            print("警告: 未设置 MOONSHOT_API_KEY 环境变量")
            print("请在 .env 文件中设置您的API密钥")
        
        # 启动Streamlit应用
        streamlit_app_path = os.path.join('src', 'streamlit_app.py')
        
        cmd = [
            sys.executable, '-m', 'streamlit', 'run', 
            streamlit_app_path,
            '--server.port=8501',
            '--server.address=localhost',
            '--browser.gatherUsageStats=false'
        ]
        
        print("正在启动Streamlit应用...")
        print(f"应用将在 http://localhost:8501 启动")
        print("按 Ctrl+C 停止应用")
        
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n应用已停止")
    except Exception as e:
        print(f"启动失败: {e}")

if __name__ == "__main__":
    main()
