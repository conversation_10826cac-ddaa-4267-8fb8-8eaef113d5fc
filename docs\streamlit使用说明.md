# Streamlit Web界面使用说明

## 概述

本项目现在支持两种使用方式：
1. **命令行界面 (CLI)** - 使用 `python main.py` 命令
2. **Web界面 (Streamlit)** - 使用浏览器访问的图形化界面

## Web界面特性

### 🌟 主要功能
- **文件上传**: 支持点击选择或拖拽上传Excel文件
- **实时处理**: 动态展示Agent处理过程和中间结果
- **智能交互**: 当Agent需要澄清信息时，提供友好的用户输入界面
- **多格式结果**: 支持表格、JSON、详细报告等多种结果展示格式
- **过程可视化**: 清晰展示工具调用、Agent分析、处理步骤等信息

### 🎨 界面设计
- **ChatGPT风格**: 类似聊天界面的交互体验
- **分类展示**: 不同类型消息使用不同组件，提高可读性
- **响应式布局**: 适配不同屏幕尺寸
- **进度指示**: 实时显示处理进度和当前步骤

## 安装和启动

### 1. 安装依赖
```bash
# 如果还没有安装streamlit
uv add streamlit

# 或者使用pip
pip install streamlit
```

### 2. 启动应用
有三种启动方式：

**方式一：使用启动脚本（推荐）**
```bash
python run_streamlit.py
```

**方式二：直接使用streamlit命令**
```bash
streamlit run src/streamlit_app.py
```

**方式三：指定端口和地址**
```bash
streamlit run src/streamlit_app.py --server.port 8501 --server.address localhost
```

### 3. 访问应用
启动后，在浏览器中访问：
```
http://localhost:8501
```

## 使用流程

### 第一步：上传文件
1. 在首页点击"选择Excel文件"或直接拖拽文件到上传区域
2. 支持 `.xlsx` 和 `.xls` 格式
3. 上传成功后点击"🚀 开始处理"

### 第二步：观察处理过程
- **系统消息**: 显示处理状态和进度
- **Agent分析**: 展示AI Agent的分析过程
- **工具调用**: 显示具体的工具执行情况
- **处理步骤**: 实时更新当前执行的步骤

### 第三步：用户交互（如需要）
当Agent需要您的帮助时：
1. 界面会显示Agent的具体问题
2. 在文本框中输入您的回答
3. 点击"📤 提交回答"继续处理
4. 如遇问题可点击"🔄 重新开始"

### 第四步：查看结果
处理完成后可以：
- **表格视图**: 查看格式化的结果表格
- **JSON视图**: 查看原始JSON数据
- **详细报告**: 阅读完整的处理报告
- **下载报告**: 保存结果到本地文件

## 界面说明

### 消息类型
- 🔧 **系统消息**: 处理状态和系统信息
- 🤖 **Agent分析**: AI Agent的思考和决策过程
- 🛠️ **工具调用**: 具体工具的执行详情
- ✅ **处理结果**: 中间处理结果
- ❓ **Agent请求**: 需要用户澄清的问题
- 👤 **用户回答**: 您的回复内容
- 📋 **处理步骤**: 当前执行的工作流步骤
- ⏳ **进度信息**: 处理进度指示

### 操作按钮
- **🚀 开始处理**: 启动文件处理流程
- **📤 提交回答**: 提交对Agent问题的回答
- **🔄 重新开始**: 重置应用，处理新文件
- **💾 下载报告**: 下载处理结果报告

## 技术特性

### 状态管理
- 使用Streamlit Session State管理应用状态
- 支持工作流的暂停和恢复
- 保持用户交互的连续性

### 工作流集成
- 完全兼容现有的LangGraph工作流
- 支持标准的interrupt机制
- 使用Command(resume=...)恢复执行

### 错误处理
- 友好的错误提示
- 自动清理临时文件
- 支持重新开始处理

## 与CLI版本的对比

| 特性 | CLI版本 | Web版本 |
|------|---------|---------|
| 文件上传 | 命令行参数 | 拖拽/点击上传 |
| 处理过程 | 文本输出 | 可视化展示 |
| 用户交互 | 命令行输入 | 图形界面输入 |
| 结果展示 | 表格/JSON | 多标签页展示 |
| 易用性 | 需要技术背景 | 用户友好 |
| 功能完整性 | 完整 | 完整 |

## 故障排除

### 常见问题

**1. 应用启动失败**
- 检查是否安装了streamlit: `pip list | grep streamlit`
- 检查端口是否被占用: 尝试使用不同端口

**2. 文件上传失败**
- 确认文件格式为 .xlsx 或 .xls
- 检查文件是否损坏
- 确认文件大小不超过限制

**3. 处理过程中断**
- 检查MOONSHOT_API_KEY是否正确设置
- 查看浏览器控制台是否有错误信息
- 尝试重新开始处理

**4. 结果显示异常**
- 刷新浏览器页面
- 检查网络连接
- 查看终端输出的错误信息

### 调试模式
如需调试，可以在终端查看详细日志：
```bash
streamlit run src/streamlit_app.py --logger.level debug
```

## 开发说明

### 文件结构
```
src/
├── streamlit_app.py    # 主应用文件
├── workflow.py         # 工作流逻辑（共享）
├── cli.py             # CLI版本（共享）
└── ...                # 其他共享模块

run_streamlit.py        # 启动脚本
test_streamlit.py       # 测试脚本
```

### 设计原则
1. **最小侵入**: 尽量不修改现有工作流代码
2. **功能对等**: 与CLI版本功能完全一致
3. **用户友好**: 提供直观的图形界面
4. **可扩展**: 易于添加新功能和改进

## 反馈和建议

如果您在使用过程中遇到问题或有改进建议，请：
1. 检查本文档的故障排除部分
2. 查看终端输出的错误信息
3. 记录重现问题的步骤
4. 提供相关的错误截图

---

**享受使用保理核额审查系统的Web界面！** 🎉
