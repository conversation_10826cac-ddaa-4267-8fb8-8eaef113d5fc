import json
from typing import Dict, List, Any
from datetime import datetime

from ..state import WorkflowState
from ..database import DatabaseManager
from ..utils import logger
from rich.text import Text
from rich.panel import Panel

class CalculationAgent:
    """核算和输出Agent"""
    
    def __init__(self):
        self.db = DatabaseManager()
        
        # 折算比例配置
        self.discount_rates = {
            "账单模式": 0.8,      # 账单模式80%
            "非账单模式": 0.7     # 非账单模式70%
        }
    
    def determine_discount_rate(self, calculation_mode: str) -> float:
        """确定折算比例"""
        if calculation_mode.startswith("非账单模式"):
            return self.discount_rates["非账单模式"]
        elif calculation_mode.startswith("账单模式"):
            return self.discount_rates["账单模式"]
        else:
            # 默认使用较低的折算比例
            return self.discount_rates["非账单模式"]
    
    def calculate_final_credit_amount(self, state: WorkflowState) -> Dict[str, Any]:
        """计算最终核定额度"""
        try:
            # 获取基本信息
            calculation_mode = state.get("calculation_mode", "未知模式")
            settlement_total = state.get("final_result", {}).get("settlement_total", 0)
            payroll_total = state.get("final_result", {}).get("payroll_total", 0)
            
            # 确定折算比例
            discount_rate = self.determine_discount_rate(calculation_mode)
            
            # 计算折后金额
            discounted_amount = settlement_total * discount_rate
            
            # 取较小值作为最终核定额度
            final_credit_amount = min(discounted_amount, payroll_total)
            
            # 获取其他信息
            summary_conflict = state.get("final_result", {}).get("summary_conflict", False)
            matched_employees = state.get("final_result", {}).get("matched_employees", 0)
            
            # 从文件路径提取项目名称
            import os
            # 优先从state中获取项目名称，如果不存在，则从文件名中提取
            project_name = state.get("project_name")
            if not project_name:
                project_name = os.path.basename(state.get("file_path", "")).replace(".xlsx", "").replace(".xls", "")
            
            return {
                "project_name": project_name,
                "calculation_mode": calculation_mode,
                "settlement_total": settlement_total,
                "discount_rate": discount_rate,
                "discounted_amount": discounted_amount,
                "payroll_total": payroll_total,
                "final_credit_amount": final_credit_amount,
                "summary_conflict": summary_conflict,
                "matched_employees": matched_employees
            }
            
        except Exception as e:
            raise Exception(f"计算最终核定额度失败: {str(e)}")
    
    def format_output_result(self, calculation_result: Dict[str, Any]) -> Dict[str, Any]:
        """格式化输出结果"""
        return {
            "项目名称": calculation_result["project_name"],
            "核额模式": calculation_result["calculation_mode"],
            "结算总额": calculation_result["settlement_total"],
            "折算比例": calculation_result["discount_rate"],
            "折后金额": calculation_result["discounted_amount"],
            "发薪金额": calculation_result["payroll_total"],
            "最终核额金额": calculation_result["final_credit_amount"],
            "汇总金额是否冲突": calculation_result["summary_conflict"],
            "匹配员工数": calculation_result["matched_employees"],
            "处理时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
    
    def generate_detailed_report(self, state: WorkflowState, calculation_result: Dict[str, Any]) -> str:
        """生成详细报告"""
        report_lines = []
        
        # 标题
        report_lines.append("=" * 50)
        report_lines.append("保理核额审查结果报告")
        report_lines.append("=" * 50)
        report_lines.append("")
        
        # 基本信息
        report_lines.append("【基本信息】")
        report_lines.append(f"项目名称: {calculation_result['project_name']}")
        report_lines.append(f"核额模式: {calculation_result['calculation_mode']}")
        report_lines.append(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")
        
        # 文件信息
        report_lines.append("【文件信息】")
        sheet_classifications = state.get("sheet_classifications", {})
        for sheet_name, classification in sheet_classifications.items():
            report_lines.append(f"- {sheet_name}: {classification}")
        report_lines.append("")
        
        # 计算结果
        report_lines.append("【计算结果】")
        report_lines.append(f"结算总额: {calculation_result['settlement_total']:,.2f} 元")
        report_lines.append(f"折算比例: {calculation_result['discount_rate']*100:.0f}%")
        report_lines.append(f"折后金额: {calculation_result['discounted_amount']:,.2f} 元")
        report_lines.append(f"发薪金额: {calculation_result['payroll_total']:,.2f} 元")
        report_lines.append(f"最终核额金额: {calculation_result['final_credit_amount']:,.2f} 元")
        report_lines.append("")
        
        # 数据一致性检查
        report_lines.append("【数据一致性检查】")
        conflict_status = "有冲突" if calculation_result['summary_conflict'] else "无冲突"
        report_lines.append(f"汇总金额冲突: {conflict_status}")
        report_lines.append(f"匹配员工数: {calculation_result['matched_employees']} 人")
        report_lines.append("")
        
        # 处理说明
        report_lines.append("【处理说明】")
        mode = calculation_result['calculation_mode']
        if "先发后融" in mode:
            report_lines.append("- 模式: 先发后融 (已结算+已发薪)")
        elif "先融后发" in mode:
            report_lines.append("- 模式: 先融后发 (已核算+未发薪)")
        
        if "账单模式" in mode:
            report_lines.append("- 数据来源: 对账单")
            report_lines.append("- 折算比例: 80%")
        elif "非账单模式" in mode:
            report_lines.append("- 数据来源: 用工统计表")
            report_lines.append("- 折算比例: 70%")
        
        report_lines.append("- 核定规则: 取折后金额与发薪金额的较小值")
        report_lines.append("")
        
        # 错误信息（如果有）
        errors = state.get("errors", [])
        if errors:
            report_lines.append("【处理过程中的问题】")
            for error in errors:
                report_lines.append(f"- {error}")
            report_lines.append("")
        
        report_lines.append("=" * 50)
        report_lines.append("报告结束")
        report_lines.append("=" * 50)
        
        return "\n".join(report_lines)
    
    def save_results(self, state: WorkflowState, calculation_result: Dict[str, Any]):
        """保存结果到数据库"""
        try:
            project_id = state.get("project_id")
            if project_id:
                self.db.save_calculation_result(project_id, calculation_result)
        except Exception as e:
            print(f"保存结果到数据库失败: {str(e)}")
    
    def display_final_result(self, state: WorkflowState, calculation_result: Dict[str, Any]):
        """使用Rich在终端上显示最终结果"""
        
        # 主标题
        logger.console.print(Panel("[bold green]保理核额审查完成[/bold green]", style="green", expand=False))

        # 最终核额
        final_amount = calculation_result['final_credit_amount']
        final_amount_text = Text(f"{final_amount:,.2f} 元", style="bold magenta", justify="center")
        logger.console.print(Panel(final_amount_text, title="[bold]最终核定额度[/bold]", expand=False))

        # 详细信息表格
        table = self._create_summary_table(state, calculation_result)
        logger.panel(table, title="[bold]详细核算报告[/bold]", style="green")

    def _create_summary_table(self, state: WorkflowState, result: Dict[str, Any]) -> str:
        """创建一个包含所有结果的Rich Table字符串"""
        table = ""
        table += f"[bold]项目名称:[/bold] {result['project_name']}\n"
        table += f"[bold]核额模式:[/bold] {result['calculation_mode']}\n"
        table += f"[bold]处理时间:[/bold] {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        table += "[bold underline]核心计算过程[/bold underline]\n"
        table += f"  结算总额: {result['settlement_total']:,.2f} 元\n"
        table += f"  折算比例: {result['discount_rate']*100:.0f}%\n"
        table += f"  折后金额: {result['discounted_amount']:,.2f} 元\n"
        table += f"  发薪总额 (匹配后): {result['payroll_total']:,.2f} 元\n"
        table += f"  [bold]核定规则:[/] 取 '折后金额' 与 '发薪总额' 的较小值\n\n"
        
        table += "[bold underline]数据一致性检查[/bold underline]\n"
        conflict_status = "[red]有冲突[/red]" if result['summary_conflict'] else "[green]无冲突[/green]"
        table += f"  对账单内合计金额校验: {conflict_status}\n"
        
        total_settlement = state.get("final_result", {}).get("total_settlement_employees", "N/A")
        total_payroll = state.get("final_result", {}).get("total_payroll_employees", "N/A")
        matched = result['matched_employees']
        unmatched = state.get("final_result", {}).get("unmatched_employees", "N/A")
        
        table += f"  人员匹配详情:\n"
        table += f"    - 对账单/用工表人数: {total_settlement}\n"
        table += f"    - 发薪表人数: {total_payroll}\n"
        table += f"    - [green]匹配成功人数: {matched}[/green]\n"
        table += f"    - [red]匹配失败人数: {unmatched}[/red]\n"

        return table

    def __call__(self, state: WorkflowState) -> WorkflowState:
        """Agent主函数"""
        logger.agent_start("最终核算 Agent")
        try:
            # 检查是否有必要的数据
            if "settlement_total" not in state.get("final_result", {}):
                error_msg = "缺少必要的结算数据，无法进行最终核算。"
                logger.error(error_msg)
                state["errors"] = state.get("errors", []) + [error_msg]
                return state
            
            logger.info("正在进行最终核算...")
            # 计算最终核定额度
            calculation_result = self.calculate_final_credit_amount(state)
            logger.success("最终核算完成。")
            
            # 在终端上显示结果
            self.display_final_result(state, calculation_result)
            
            # 生成详细报告（用于保存）
            detailed_report = self.generate_detailed_report(state, calculation_result)
            
            # 保存结果到数据库
            logger.info("正在保存最终结果到数据库...")
            self.save_results(state, calculation_result)
            logger.success("结果保存成功。")
            
            # 更新状态
            state["final_result"] = {
                **state.get("final_result", {}),
                **calculation_result,
                "detailed_report": detailed_report
            }
            state["current_step"] = "calculation_completed"
            
            logger.agent_end("最终核算 Agent")
            return state
            
        except Exception as e:
            error_msg = f"核算处理失败: {str(e)}"
            logger.error(error_msg)
            state["errors"] = state.get("errors", []) + [error_msg]
            logger.agent_end("最终核算 Agent")
            return state