#!/usr/bin/env python3

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """测试导入是否正常"""
    try:
        print("测试导入...")
        
        # 测试基本导入
        import streamlit as st
        print("✅ Streamlit导入成功")
        
        # 测试项目模块导入
        from src.streamlit_app import initialize_session_state, add_message
        print("✅ 项目模块导入成功")
        
        # 测试工作流导入
        from src.workflow import HesuanWorkflow
        print("✅ 工作流模块导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    try:
        print("\n测试基本功能...")
        
        # 测试session state初始化
        from src.streamlit_app import initialize_session_state
        print("✅ Session state初始化函数可用")
        
        # 测试消息添加
        from src.streamlit_app import add_message
        print("✅ 消息添加函数可用")
        
        # 测试工作流创建
        from src.workflow import HesuanWorkflow
        workflow = HesuanWorkflow()
        print("✅ 工作流实例创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 开始测试Streamlit应用...")
    
    # 检查环境变量
    if not os.getenv('MOONSHOT_API_KEY'):
        print("⚠️  警告: 未设置 MOONSHOT_API_KEY 环境变量")
    else:
        print("✅ MOONSHOT_API_KEY 已设置")
    
    # 运行测试
    tests_passed = 0
    total_tests = 2
    
    if test_imports():
        tests_passed += 1
    
    if test_basic_functionality():
        tests_passed += 1
    
    # 输出结果
    print(f"\n📊 测试结果: {tests_passed}/{total_tests} 通过")
    
    if tests_passed == total_tests:
        print("🎉 所有测试通过！应用应该可以正常启动。")
        print("\n启动应用:")
        print("python run_streamlit.py")
        print("或者:")
        print("streamlit run src/streamlit_app.py")
    else:
        print("❌ 部分测试失败，请检查错误信息。")
    
    return tests_passed == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
