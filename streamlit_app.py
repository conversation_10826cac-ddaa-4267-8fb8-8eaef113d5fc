#!/usr/bin/env python3

import streamlit as st
import os
import tempfile
import json
from pathlib import Path
from dotenv import load_dotenv
from datetime import datetime
from typing import Dict, List, Any, Optional

# 加载环境变量
load_dotenv()

# 添加src目录到Python路径
import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# 导入工作流相关模块
from src.workflow import HesuanWorkflow
from langchain_core.messages import AIMessage
from langgraph.types import Command

def initialize_session_state():
    """初始化Streamlit session state"""
    if 'stage' not in st.session_state:
        st.session_state.stage = 'upload'  # 'upload', 'processing', 'waiting_user', 'completed'
    
    if 'uploaded_file' not in st.session_state:
        st.session_state.uploaded_file = None
    
    if 'file_path' not in st.session_state:
        st.session_state.file_path = None
    
    if 'workflow' not in st.session_state:
        st.session_state.workflow = None
    
    if 'config' not in st.session_state:
        st.session_state.config = None
    
    if 'messages' not in st.session_state:
        st.session_state.messages = []
    
    if 'final_result' not in st.session_state:
        st.session_state.final_result = None
    
    if 'pending_question' not in st.session_state:
        st.session_state.pending_question = None
    
    if 'processing_step' not in st.session_state:
        st.session_state.processing_step = None

def add_message(msg_type: str, content: str, step: Optional[str] = None):
    """添加消息到消息历史"""
    message = {
        'type': msg_type,
        'content': content,
        'timestamp': datetime.now().strftime("%H:%M:%S"),
        'step': step
    }
    st.session_state.messages.append(message)

def display_message(message: Dict[str, Any]):
    """显示单条消息"""
    msg_type = message['type']
    content = message['content']
    timestamp = message.get('timestamp', '')
    step = message.get('step', '')

    if msg_type == 'system':
        st.info(f"🔧 **系统** ({timestamp}): {content}")

    elif msg_type == 'agent':
        with st.chat_message("assistant", avatar="🤖"):
            st.markdown(f"**Agent分析** ({timestamp})")
            st.markdown(content)
            if step:
                st.caption(f"处理步骤: {step}")

    elif msg_type == 'tool':
        with st.expander(f"🛠️ 工具调用 ({timestamp})", expanded=False):
            if step:
                st.caption(f"步骤: {step}")
            st.code(content, language='text')

    elif msg_type == 'result':
        st.success(f"✅ **处理结果** ({timestamp})")
        # 尝试解析JSON格式的结果
        try:
            if content.startswith('{') or content.startswith('['):
                result_data = json.loads(content)
                st.json(result_data)
            else:
                st.markdown(content)
        except:
            st.markdown(content)

    elif msg_type == 'user_request':
        st.warning(f"❓ **Agent请求您的帮助** ({timestamp})")
        st.markdown(f"> {content}")

    elif msg_type == 'user_response':
        with st.chat_message("user", avatar="👤"):
            st.markdown(f"**您的回答** ({timestamp})")
            st.markdown(content)

    elif msg_type == 'step':
        st.info(f"📋 **{step}** ({timestamp}): {content}")

    elif msg_type == 'error':
        st.error(f"❌ **错误** ({timestamp}): {content}")

    elif msg_type == 'progress':
        st.info(f"⏳ **进度** ({timestamp}): {content}")
        if 'progress' in message:
            st.progress(message['progress'])

def display_messages():
    """显示所有消息历史"""
    if st.session_state.messages:
        st.subheader("处理过程")
        for message in st.session_state.messages:
            display_message(message)

def main():
    """主应用函数"""
    # 页面配置
    st.set_page_config(
        page_title="保理核额审查系统",
        page_icon="📊",
        layout="wide",
        initial_sidebar_state="collapsed"
    )
    
    # 初始化session state
    initialize_session_state()
    
    # 页面标题
    st.title("📊 保理核额审查多Agent系统")
    st.markdown("---")
    
    # 根据当前阶段显示不同内容
    if st.session_state.stage == 'upload':
        show_upload_interface()
    
    elif st.session_state.stage == 'processing':
        show_processing_interface()
    
    elif st.session_state.stage == 'waiting_user':
        show_user_input_interface()
    
    elif st.session_state.stage == 'completed':
        show_results_interface()
    
    # 显示消息历史
    if st.session_state.messages:
        st.markdown("---")
        display_messages()

def show_upload_interface():
    """显示文件上传界面"""
    st.subheader("📁 文件上传")
    st.markdown("请上传需要处理的Excel文件（支持.xlsx和.xls格式）")
    
    # 文件上传组件
    uploaded_file = st.file_uploader(
        "选择Excel文件",
        type=['xlsx', 'xls'],
        help="支持点击选择或直接拖拽文件到此区域"
    )
    
    if uploaded_file is not None:
        # 显示文件信息
        st.success(f"✅ 文件已上传: {uploaded_file.name}")
        st.info(f"文件大小: {uploaded_file.size / 1024:.1f} KB")
        
        # 开始处理按钮
        if st.button("🚀 开始处理", type="primary"):
            handle_file_upload(uploaded_file)

def show_processing_interface():
    """显示处理中界面"""
    st.subheader("⚙️ 正在处理...")

    # 显示当前处理步骤
    if st.session_state.processing_step:
        st.info(f"当前步骤: {st.session_state.processing_step}")

    # 显示进度指示器
    with st.spinner("Agent正在分析和处理您的文件..."):
        # 执行工作流处理
        process_workflow()

def show_user_input_interface():
    """显示用户输入界面"""
    st.subheader("💬 Agent需要您的帮助")

    if st.session_state.pending_question:
        # 显示Agent的问题
        with st.container():
            st.markdown("### 🤖 Agent的问题")
            st.info(st.session_state.pending_question)

        st.markdown("### 💭 您的回答")

        # 用户输入框 - 使用text_area支持多行输入
        user_response = st.text_area(
            "请详细回答Agent的问题:",
            key="user_input",
            placeholder="请在此输入您的回答...\n\n提示：您可以输入多行文本，Agent会根据您的回答继续处理。",
            height=100
        )

        # 按钮布局
        col1, col2 = st.columns([1, 1])

        with col1:
            if st.button("📤 提交回答", type="primary", disabled=not user_response.strip()):
                handle_user_response(user_response)

        with col2:
            if st.button("🔄 重新开始"):
                reset_application()

        # 显示帮助信息
        with st.expander("💡 需要帮助？", expanded=False):
            st.markdown("""
            **如何回答Agent的问题：**

            1. **仔细阅读**Agent的问题，理解它需要什么信息
            2. **详细回答**，提供尽可能准确的信息
            3. **如果不确定**，可以说明您的疑虑，Agent会进一步澄清
            4. **如果遇到问题**，可以点击"重新开始"重新上传文件

            **常见问题类型：**
            - 确认数据列的含义
            - 选择处理方式
            - 提供缺失的参数值
            """)
    else:
        st.error("没有待处理的问题")

def show_results_interface():
    """显示结果界面"""
    st.subheader("📋 处理结果")

    if st.session_state.final_result:
        st.success("✅ 文件处理完成！")

        result_data = st.session_state.final_result.get("final_result", {})

        if result_data:
            # 显示结果选项卡
            tab1, tab2, tab3 = st.tabs(["📊 表格视图", "📄 JSON视图", "📝 详细报告"])

            with tab1:
                display_table_result(result_data)

            with tab2:
                display_json_result(result_data)

            with tab3:
                display_report_result(result_data)
        else:
            st.warning("没有找到处理结果数据")
            st.json(st.session_state.final_result)

    # 操作按钮
    col1, col2 = st.columns([1, 1])

    with col1:
        if st.button("🔄 处理新文件", type="primary"):
            reset_application()

    with col2:
        if st.button("💾 下载报告"):
            download_report()

def display_table_result(result_data: dict):
    """以表格形式显示结果"""
    formatted_result = result_data.get("formatted_result", {})

    if not formatted_result:
        st.warning("没有格式化的结果数据")
        return

    # 创建结果表格
    st.markdown("### 核额审查结果汇总")

    # 基本信息
    col1, col2 = st.columns(2)

    with col1:
        st.metric("项目名称", formatted_result.get("项目名称", "未知"))
        st.metric("核额模式", formatted_result.get("核额模式", "未知"))
        st.metric("结算总额", f"{formatted_result.get('结算总额', 0):,.2f} 元")
        st.metric("发薪金额", f"{formatted_result.get('发薪金额', 0):,.2f} 元")

    with col2:
        st.metric("折算比例", f"{formatted_result.get('折算比例', 0)*100:.0f}%")
        st.metric("折后金额", f"{formatted_result.get('折后金额', 0):,.2f} 元")
        st.metric("最终核额金额", f"{formatted_result.get('最终核额金额', 0):,.2f} 元",
                 delta=None, delta_color="normal")

        # 冲突检查
        conflict = formatted_result.get("汇总金额是否冲突", False)
        conflict_text = "是" if conflict else "否"
        conflict_color = "red" if conflict else "green"
        st.markdown(f"**汇总金额冲突**: :{conflict_color}[{conflict_text}]")

    # 详细数据表格
    if "详细数据" in formatted_result:
        st.markdown("### 详细数据")
        detail_data = formatted_result["详细数据"]
        if isinstance(detail_data, list) and detail_data:
            import pandas as pd
            df = pd.DataFrame(detail_data)
            st.dataframe(df, use_container_width=True)

def display_json_result(result_data: dict):
    """以JSON形式显示结果"""
    formatted_result = result_data.get("formatted_result", {})

    if formatted_result:
        st.markdown("### JSON格式结果")
        st.json(formatted_result)
    else:
        st.warning("没有格式化的结果数据")
        st.json(result_data)

def display_report_result(result_data: dict):
    """显示详细报告"""
    detailed_report = result_data.get("detailed_report", "")

    if detailed_report:
        st.markdown("### 详细报告")
        st.text_area("报告内容", detailed_report, height=400, disabled=True)
    else:
        st.warning("没有生成详细报告")

def download_report():
    """下载报告功能"""
    if st.session_state.final_result:
        result_data = st.session_state.final_result.get("final_result", {})
        detailed_report = result_data.get("detailed_report", "")

        if detailed_report:
            st.download_button(
                label="📄 下载详细报告",
                data=detailed_report,
                file_name=f"核额报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                mime="text/plain"
            )
        else:
            # 下载JSON格式结果
            json_data = json.dumps(result_data, ensure_ascii=False, indent=2)
            st.download_button(
                label="📄 下载JSON结果",
                data=json_data,
                file_name=f"核额结果_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                mime="application/json"
            )

def handle_file_upload(uploaded_file):
    """处理文件上传"""
    try:
        # 保存文件到临时位置
        with tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as tmp_file:
            tmp_file.write(uploaded_file.getvalue())
            file_path = tmp_file.name

        # 创建工作流实例
        workflow = HesuanWorkflow()

        # 为本次处理创建一个基于文件路径的确定性会话ID
        thread_id = f"thread_{os.path.abspath(file_path)}".encode('utf-8').hex()
        config = {"configurable": {"thread_id": thread_id}}

        # 更新session state
        st.session_state.uploaded_file = uploaded_file
        st.session_state.file_path = file_path
        st.session_state.workflow = workflow
        st.session_state.config = config
        st.session_state.stage = 'processing'

        # 添加系统消息
        add_message('system', f"文件 {uploaded_file.name} 上传成功，开始处理...")

        # 重新运行以进入处理阶段
        st.rerun()

    except Exception as e:
        st.error(f"文件上传失败: {str(e)}")

def handle_user_response(user_response: str):
    """处理用户回复"""
    # 添加用户回复消息
    add_message('user_response', user_response)

    # 清除待处理问题
    st.session_state.pending_question = None

    # 恢复工作流执行
    resume_workflow_with_user_response(user_response)

    # 重新运行
    st.rerun()

def process_workflow():
    """处理工作流执行"""
    try:
        if not st.session_state.workflow or not st.session_state.file_path:
            st.error("工作流或文件路径未初始化")
            return

        workflow = st.session_state.workflow
        config = st.session_state.config
        file_path = st.session_state.file_path

        # 获取初始状态
        inputs = workflow.get_initial_state(file_path)

        # 添加处理开始消息
        add_message('system', "开始执行工作流...")
        add_message('step', "正在初始化工作流...", "初始化")

        # 执行工作流stream
        stream = workflow.app.stream(inputs, config=config, stream_mode="values")
        final_result = None

        # 步骤名称映射
        step_names = {
            "file_parser": "📁 文件解析与分类",
            "account_bill": "📊 对账单处理",
            "work_statistics": "📈 用工统计表处理",
            "payroll": "💰 发薪流水处理",
            "calculation": "🧮 核算与输出"
        }

        # 处理stream中的所有事件
        event_count = 0
        for event in stream:
            final_result = event
            event_count += 1

            # 记录处理进度
            add_message('progress', f"正在处理第 {event_count} 个工作流事件...")

            # 检查是否有新的处理步骤
            if 'processing_step' in event:
                step = event['processing_step']
                st.session_state.processing_step = step
                step_display = step_names.get(step, step)
                add_message('step', f"开始执行: {step_display}", step)

            # 检查是否有消息更新
            if 'messages' in event and event['messages']:
                last_msg = event['messages'][-1]
                if hasattr(last_msg, 'content') and last_msg.content:
                    add_message('agent', last_msg.content)

        # 检查最后的状态
        messages = final_result.get("messages", [])
        if not messages:
            # 工作流完成
            st.session_state.final_result = final_result
            st.session_state.stage = 'completed'
            add_message('system', "工作流处理完成")
            st.rerun()
            return

        last_message = messages[-1]

        # 检查是否需要用户输入
        if (isinstance(last_message, AIMessage) and
            last_message.tool_calls and
            last_message.tool_calls[0]['name'] == 'ask_user_for_clarification'):

            # 需要用户输入
            tool_call = last_message.tool_calls[0]
            question = tool_call['args']['question']

            st.session_state.pending_question = question
            st.session_state.stage = 'waiting_user'

            add_message('user_request', question)
            st.rerun()
            return

        # 工作流正常完成
        st.session_state.final_result = final_result
        st.session_state.stage = 'completed'
        add_message('system', "工作流处理完成")
        st.rerun()

    except Exception as e:
        st.error(f"工作流处理失败: {str(e)}")
        add_message('error', f"处理失败: {str(e)}")

def resume_workflow_with_user_response(user_response: str):
    """使用用户回复恢复工作流"""
    try:
        workflow = st.session_state.workflow
        config = st.session_state.config

        if not workflow or not config:
            st.error("工作流状态丢失")
            return

        # 使用Command(resume=...)恢复工作流
        stream = workflow.app.stream(Command(resume=user_response), config=config, stream_mode="values")
        final_result = None

        # 处理恢复后的stream
        for event in stream:
            final_result = event

        # 检查是否还需要更多用户输入
        messages = final_result.get("messages", [])
        if messages:
            last_message = messages[-1]

            if (isinstance(last_message, AIMessage) and
                last_message.tool_calls and
                last_message.tool_calls[0]['name'] == 'ask_user_for_clarification'):

                # 还需要更多用户输入
                tool_call = last_message.tool_calls[0]
                question = tool_call['args']['question']

                st.session_state.pending_question = question
                add_message('user_request', question)
                return

        # 工作流完成
        st.session_state.final_result = final_result
        st.session_state.stage = 'completed'
        add_message('system', "工作流处理完成")

    except Exception as e:
        st.error(f"恢复工作流失败: {str(e)}")
        add_message('system', f"恢复失败: {str(e)}")

def reset_application():
    """重置应用状态"""
    # 清理临时文件
    if st.session_state.file_path and os.path.exists(st.session_state.file_path):
        os.unlink(st.session_state.file_path)

    # 重置所有状态
    for key in list(st.session_state.keys()):
        del st.session_state[key]

    # 重新运行
    st.rerun()

if __name__ == "__main__":
    main()
